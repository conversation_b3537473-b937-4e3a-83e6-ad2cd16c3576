import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AuthDebugService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Comprehensive authentication debug check
  static Future<void> debugAuthenticationStatus() async {
    print('\n🔍 === AUTHENTICATION DEBUG === 🔍');

    // Check Firebase Auth status
    final user = _auth.currentUser;
    print('1. Firebase Auth Status:');
    print('   - User authenticated: ${user != null}');

    if (user != null) {
      print('   - User ID: ${user.uid}');
      print('   - Email: ${user.email ?? "No email"}');
      print('   - Phone: ${user.phoneNumber ?? "No phone"}');
      print('   - Email verified: ${user.emailVerified}');
      print('   - Anonymous: ${user.isAnonymous}');
      print('   - Creation time: ${user.metadata.creationTime}');
      print('   - Last sign in: ${user.metadata.lastSignInTime}');

      // Check ID token
      try {
        final idToken = await user.getIdToken();
        if (idToken != null && idToken.isNotEmpty) {
          final tokenPreview =
              idToken.length > 50 ? idToken.substring(0, 50) : idToken;
          print('   - ID Token: $tokenPreview...');
        } else {
          print('   - ID Token: null or empty');
        }

        // Check token claims
        final idTokenResult = await user.getIdTokenResult();
        print('   - Token claims: ${idTokenResult.claims}');
      } catch (e) {
        print('   - Error getting ID token: $e');
      }
    } else {
      print('   - No user logged in!');
      print('\n❌ USER NOT AUTHENTICATED - THIS IS THE PROBLEM! ❌');
      print('Please ensure user is logged in before accessing Firestore.');
      return;
    }

    // Check Firestore user document
    print('\n2. Firestore User Document:');
    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        print('   - User document exists: ✅');
        final userData = userDoc.data();
        if (userData != null) {
          print('   - User data: $userData');
          print('   - Role: ${userData['role'] ?? "No role"}');
          print('   - Active: ${userData['isActive'] ?? "No isActive field"}');
        } else {
          print('   - User document exists but data is null');
        }
      } else {
        print('   - User document missing: ❌');
        print('   - This could cause permission issues!');
      }
    } catch (e) {
      print('   - Error checking user document: $e');
    }

    // Check admin user document
    print('\n3. Admin User Document:');
    try {
      final adminDoc =
          await _firestore.collection('admin_users').doc(user.uid).get();
      if (adminDoc.exists) {
        print('   - Admin document exists: ✅');
        final adminData = adminDoc.data();
        if (adminData != null) {
          print('   - Admin data: $adminData');
          print('   - Active: ${adminData['isActive'] ?? "No isActive field"}');
        } else {
          print('   - Admin document exists but data is null');
        }
      } else {
        print('   - Admin document missing: ℹ️ (Normal for regular users)');
      }
    } catch (e) {
      print('   - Error checking admin document: $e');
    }

    // Test basic Firestore access
    print('\n4. Basic Firestore Access Test:');
    try {
      // Try to read from test collection
      await _firestore.collection('test-connection').doc('test').get();
      print('   - Basic Firestore access: ✅');
    } catch (e) {
      print('   - Basic Firestore access failed: ❌');
      print('   - Error: $e');
    }

    // Test exams collection access
    print('\n5. Exams Collection Access Test:');
    try {
      final snapshot = await _firestore.collection('exams').limit(1).get();
      print('   - Exams collection read: ✅');
      print('   - Documents found: ${snapshot.docs.length}');
    } catch (e) {
      print('   - Exams collection read failed: ❌');
      print('   - Error: $e');

      if (e.toString().contains('permission-denied')) {
        print('\n🚨 PERMISSION DENIED ERROR DETECTED! 🚨');
        print('This means your Firebase rules are blocking access.');
        print(
            'Please update your Firestore rules to allow authenticated users.');
      }
    }

    print('\n=== END AUTHENTICATION DEBUG ===\n');
  }

  /// Quick auth status check
  static bool isUserAuthenticated() {
    return _auth.currentUser != null;
  }

  /// Get current user info
  static Map<String, dynamic>? getCurrentUserInfo() {
    final user = _auth.currentUser;
    if (user == null) return null;

    return {
      'uid': user.uid,
      'email': user.email,
      'phone': user.phoneNumber,
      'emailVerified': user.emailVerified,
      'isAnonymous': user.isAnonymous,
    };
  }

  /// Force refresh authentication token
  static Future<void> refreshAuthToken() async {
    final user = _auth.currentUser;
    if (user != null) {
      try {
        await user.getIdToken(true); // Force refresh
        print('Auth token refreshed successfully');
      } catch (e) {
        print('Error refreshing auth token: $e');
      }
    }
  }

  /// Sign out and clear authentication
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      print('User signed out successfully');
    } catch (e) {
      print('Error signing out: $e');
    }
  }
}

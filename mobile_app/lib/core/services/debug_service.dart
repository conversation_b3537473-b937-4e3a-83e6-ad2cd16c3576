import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class DebugService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Test Firebase connection and exam data
  static Future<Map<String, dynamic>> testFirebaseConnection() async {
    try {
      // Test basic connection
      final snapshot = await _firestore.collection('exams').get();

      final result = <String, dynamic>{
        'success': true,
        'totalDocuments': snapshot.docs.length,
        'documents': <Map<String, dynamic>>[],
        'errors': <String>[],
      };

      // Analyze each document
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data();
          final docInfo = {
            'id': doc.id,
            'hasName': data.containsKey('name'),
            'hasExamType': data.containsKey('examType'),
            'hasQuestions': data.containsKey('questions'),
            'isActive': data['isActive'] ?? false,
            'questionCount': (data['questions'] as List?)?.length ?? 0,
            'name': data['name'] ?? 'No name',
            'examType': data['examType'] ?? 'No type',
            'suitableFor': data['suitableFor'] ?? [],
            'timeLimit': data['timeLimit'] ?? 0,
            'numberOfQuestions': data['numberOfQuestions'] ?? 0,
            'createdAt': data['createdAt']?.toString() ?? 'No date',
            'rawData': data,
          };

          (result['documents'] as List<Map<String, dynamic>>).add(docInfo);

          if (kDebugMode) {
            print('Exam Document Analysis:');
            print('ID: ${doc.id}');
            print('Name: ${data['name']}');
            print('Type: ${data['examType']}');
            print('Active: ${data['isActive']}');
            print('Questions: ${(data['questions'] as List?)?.length ?? 0}');
            print('Raw Data: $data');
            print('---');
          }
        } catch (e) {
          (result['errors'] as List<String>)
              .add('Error processing document ${doc.id}: $e');
        }
      }

      return result;
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'totalDocuments': 0,
        'documents': [],
        'errors': [e.toString()],
      };
    }
  }

  /// Test the exam stream specifically
  static Stream<Map<String, dynamic>> testExamStream() {
    return _firestore
        .collection('exams')
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final result = {
        'timestamp': DateTime.now().toIso8601String(),
        'totalDocs': snapshot.docs.length,
        'activeExams': 0,
        'examsWithQuestions': 0,
        'documents': <Map<String, dynamic>>[],
      };

      for (var doc in snapshot.docs) {
        final data = doc.data();
        final questions = data['questions'] as List? ?? [];

        if (data['isActive'] == true) {
          result['activeExams'] = (result['activeExams'] as int) + 1;
        }

        if (questions.isNotEmpty) {
          result['examsWithQuestions'] =
              (result['examsWithQuestions'] as int) + 1;
        }

        (result['documents'] as List<Map<String, dynamic>>).add({
          'id': doc.id,
          'name': data['name'],
          'examType': data['examType'],
          'isActive': data['isActive'],
          'questionCount': questions.length,
          'hasQuestions': questions.isNotEmpty,
        });
      }

      return result;
    });
  }

  /// Create a test exam document
  static Future<String> createTestExam() async {
    try {
      final testExam = {
        'name': 'Test Exam - Mobile Debug',
        'examType': 'Custom',
        'numberOfQuestions': 2,
        'timeLimit': 10,
        'suitableFor': ['MTS', 'Postman'],
        'questions': [
          {
            'id': 'q1',
            'question': 'What is 2 + 2?',
            'options': ['3', '4', '5', '6'],
            'correctAnswer': 1,
            'explanation': 'Basic arithmetic',
          },
          {
            'id': 'q2',
            'question': 'What is the capital of India?',
            'options': ['Mumbai', 'Delhi', 'Kolkata', 'Chennai'],
            'correctAnswer': 1,
            'explanation': 'Delhi is the capital',
          },
        ],
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'isActive': true,
      };

      final docRef = await _firestore.collection('exams').add(testExam);
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create test exam: $e');
    }
  }

  /// Delete test exam
  static Future<void> deleteTestExam(String examId) async {
    try {
      await _firestore.collection('exams').doc(examId).delete();
    } catch (e) {
      throw Exception('Failed to delete test exam: $e');
    }
  }

  /// Check Firestore security rules
  static Future<Map<String, dynamic>> checkSecurityRules() async {
    try {
      // Try to read
      final readTest = await _firestore.collection('exams').limit(1).get();

      // Try to write (this will fail if rules don't allow it)
      final writeTest = await _firestore.collection('test').add({
        'timestamp': Timestamp.now(),
        'test': true,
      });

      // Clean up test document
      await writeTest.delete();

      return {
        'canRead': true,
        'canWrite': true,
        'readCount': readTest.docs.length,
      };
    } catch (e) {
      return {
        'canRead': false,
        'canWrite': false,
        'error': e.toString(),
      };
    }
  }
}

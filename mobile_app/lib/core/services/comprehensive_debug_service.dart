import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ComprehensiveDebugService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Run all debug checks to identify the permission issue
  static Future<void> runFullDiagnostics() async {
    print('\n🚨 === COMPREHENSIVE FIREBASE DIAGNOSTICS === 🚨\n');

    await _checkFirebaseAuth();
    await _checkFirestoreConnection();
    await _checkProjectConfiguration();
    await _checkRulesWithDifferentApproaches();

    print('\n🏁 === DIAGNOSTICS COMPLETE === 🏁\n');
  }

  static Future<void> _checkFirebaseAuth() async {
    print('1️⃣ === FIREBASE AUTH CHECK ===');

    final user = _auth.currentUser;
    if (user == null) {
      print('❌ CRITICAL: No user authenticated!');
      print('   This is likely the root cause of permission denied.');
      print('   Please ensure user is logged in before accessing Firestore.');
      return;
    }

    print('✅ User authenticated');
    print('   - UID: ${user.uid}');
    print('   - Email: ${user.email ?? "No email"}');
    print('   - Phone: ${user.phoneNumber ?? "No phone"}');
    print('   - Email verified: ${user.emailVerified}');
    print('   - Anonymous: ${user.isAnonymous}');
    print(
        '   - Provider: ${user.providerData.map((p) => p.providerId).join(", ")}');

    // Check token
    try {
      final token = await user.getIdToken(true); // Force refresh
      if (token != null) {
        print('   - Token obtained: ✅ (${token.length} chars)');
      } else {
        print('   - Token obtained: ❌ (null)');
      }

      final tokenResult = await user.getIdTokenResult(true);
      print('   - Token issued at: ${tokenResult.issuedAtTime}');
      print('   - Token expires at: ${tokenResult.expirationTime}');
      print('   - Auth time: ${tokenResult.authTime}');
      print('   - Claims: ${tokenResult.claims}');
    } catch (e) {
      print('   - Token error: ❌ $e');
    }

    print('');
  }

  static Future<void> _checkFirestoreConnection() async {
    print('2️⃣ === FIRESTORE CONNECTION CHECK ===');

    try {
      // Test basic connection
      print('Testing basic Firestore connection...');
      final settings = _firestore.settings;
      print('   - Host: ${settings.host}');
      print('   - SSL enabled: ${settings.sslEnabled}');
      print('   - Persistence enabled: ${settings.persistenceEnabled}');

      // Test with simplest possible operation
      print('Testing simplest read operation...');
      await _firestore.collection('_test_').doc('_test_').get();
      print('   - Basic connection: ✅');
    } catch (e) {
      print('   - Connection failed: ❌');
      print('   - Error: $e');

      if (e.toString().contains('permission-denied')) {
        print('   - This suggests rules are still blocking access');
      } else if (e.toString().contains('unavailable')) {
        print('   - This suggests network/connectivity issues');
      }
    }

    print('');
  }

  static Future<void> _checkProjectConfiguration() async {
    print('3️⃣ === PROJECT CONFIGURATION CHECK ===');

    try {
      // Check if we can access app metadata
      final app = _firestore.app;
      print('   - App name: ${app.name}');
      print('   - Project ID: ${app.options.projectId}');
      print('   - API Key: ${app.options.apiKey.substring(0, 10)}...');

      // Verify we're connecting to the right project
      if (app.options.projectId != 'mcq-quiz-system') {
        print('   - ⚠️  WARNING: Project ID mismatch!');
        print('   - Expected: mcq-quiz-system');
        print('   - Actual: ${app.options.projectId}');
      } else {
        print('   - Project ID correct: ✅');
      }
    } catch (e) {
      print('   - Configuration check failed: ❌ $e');
    }

    print('');
  }

  static Future<void> _checkRulesWithDifferentApproaches() async {
    print('4️⃣ === RULES TESTING WITH DIFFERENT APPROACHES ===');

    final user = _auth.currentUser;
    if (user == null) {
      print('❌ Cannot test rules - user not authenticated');
      return;
    }

    // Test 1: Try to read from a collection that should always work
    print('Test 1: Reading from test collection...');
    try {
      await _firestore.collection('test').doc('test').get();
      print('   - Test collection read: ✅');
    } catch (e) {
      print('   - Test collection read: ❌ $e');
    }

    // Test 2: Try to write to test collection
    print('Test 2: Writing to test collection...');
    try {
      await _firestore.collection('test').doc('test').set({
        'timestamp': FieldValue.serverTimestamp(),
        'user': user.uid,
        'test': true,
      });
      print('   - Test collection write: ✅');
    } catch (e) {
      print('   - Test collection write: ❌ $e');
    }

    // Test 3: Try to read from exams collection
    print('Test 3: Reading from exams collection...');
    try {
      final snapshot = await _firestore.collection('exams').limit(1).get();
      print('   - Exams collection read: ✅ (${snapshot.docs.length} docs)');
    } catch (e) {
      print('   - Exams collection read: ❌ $e');

      if (e is FirebaseException) {
        print('   - Firebase error code: ${e.code}');
        print('   - Firebase error message: ${e.message}');
        print('   - Firebase error plugin: ${e.plugin}');
      }
    }

    // Test 4: Try different query approaches
    print('Test 4: Different query approaches...');

    // Simple get
    try {
      await _firestore.collection('exams').get();
      print('   - Simple get(): ✅');
    } catch (e) {
      print('   - Simple get(): ❌ $e');
    }

    // With where clause
    try {
      await _firestore
          .collection('exams')
          .where('isActive', isEqualTo: true)
          .get();
      print('   - With where clause: ✅');
    } catch (e) {
      print('   - With where clause: ❌ $e');
    }

    // With limit
    try {
      await _firestore.collection('exams').limit(1).get();
      print('   - With limit: ✅');
    } catch (e) {
      print('   - With limit: ❌ $e');
    }

    print('');
  }

  /// Test specific collection access
  static Future<void> testCollectionAccess(String collectionName) async {
    print('🔍 Testing access to collection: $collectionName');

    final user = _auth.currentUser;
    if (user == null) {
      print('❌ User not authenticated');
      return;
    }

    try {
      // Test read
      final snapshot =
          await _firestore.collection(collectionName).limit(1).get();
      print('   - Read access: ✅ (${snapshot.docs.length} docs found)');

      // Test write
      await _firestore.collection(collectionName).add({
        'test': true,
        'timestamp': FieldValue.serverTimestamp(),
        'user': user.uid,
      });
      print('   - Write access: ✅');
    } catch (e) {
      print('   - Access failed: ❌ $e');

      if (e is FirebaseException) {
        print('   - Error code: ${e.code}');
        print('   - Error message: ${e.message}');
      }
    }
  }

  /// Check if rules are actually deployed
  static Future<void> checkRulesDeployment() async {
    print('🔍 === CHECKING RULES DEPLOYMENT ===');

    // Try operations that should work with permissive rules
    final testOperations = [
      () => _firestore.collection('test-rules').doc('test').get(),
      () => _firestore.collection('test-rules').doc('test').set({'test': true}),
      () => _firestore.collection('exams').limit(1).get(),
      () => _firestore.collection('users').limit(1).get(),
    ];

    for (int i = 0; i < testOperations.length; i++) {
      try {
        await testOperations[i]();
        print('   - Test operation ${i + 1}: ✅');
      } catch (e) {
        print('   - Test operation ${i + 1}: ❌ $e');

        if (e.toString().contains('permission-denied')) {
          print(
              '     → This suggests rules are not permissive or not deployed');
        }
      }
    }
  }

  /// Force refresh everything
  static Future<void> forceRefreshAndRetry() async {
    print('🔄 === FORCE REFRESH AND RETRY ===');

    final user = _auth.currentUser;
    if (user != null) {
      try {
        // Force refresh auth token
        await user.getIdToken(true);
        print('   - Auth token refreshed: ✅');

        // Wait a moment for token to propagate
        await Future.delayed(Duration(seconds: 2));

        // Retry exam access
        final snapshot = await _firestore.collection('exams').limit(1).get();
        print('   - Retry exam access: ✅ (${snapshot.docs.length} docs)');
      } catch (e) {
        print('   - Retry failed: ❌ $e');
      }
    }
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/exam_model.dart';

/// Secure exam service that respects Firebase security rules
/// and handles authentication/authorization properly
class SecureExamService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const String _collection = 'exams';

  /// Check if user is authenticated
  static bool get isAuthenticated => _auth.currentUser != null;

  /// Get current user ID
  static String? get currentUserId => _auth.currentUser?.uid;

  /// Get all active exams (respects security rules)
  static Future<List<ExamModel>> getActiveExams() async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to fetch exams');
    }

    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .toList();
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: User not authorized to read exams');
      } else if (e.code == 'unavailable') {
        throw Exception('Service unavailable: Please check your internet connection');
      }
      throw Exception('Failed to fetch exams: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch exams: $e');
    }
  }

  /// Get featured exams (recent active exams with questions)
  static Future<List<ExamModel>> getFeaturedExams({int limit = 5}) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to fetch featured exams');
    }

    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .where((exam) => exam.questions.isNotEmpty)
          .toList();

      return exams;
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: User not authorized to read featured exams');
      }
      throw Exception('Failed to fetch featured exams: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch featured exams: $e');
    }
  }

  /// Get exam by ID (only if active or user is admin)
  static Future<ExamModel?> getExamById(String examId) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to fetch exam');
    }

    try {
      final doc = await _firestore.collection(_collection).doc(examId).get();

      if (doc.exists) {
        return ExamModel.fromFirestore(doc);
      }
      return null;
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: Exam not accessible or user not authorized');
      }
      throw Exception('Failed to fetch exam: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch exam: $e');
    }
  }

  /// Get exams suitable for specific role
  static Future<List<ExamModel>> getExamsForRole(String role) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to fetch exams');
    }

    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('suitableFor', arrayContains: role)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .where((exam) => exam.questions.isNotEmpty)
          .toList();
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: User not authorized to read exams for role');
      }
      throw Exception('Failed to fetch exams for role: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch exams for role: $e');
    }
  }

  /// Search exams by name or type (client-side filtering for security)
  static Future<List<ExamModel>> searchExams(String query) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to search exams');
    }

    try {
      // Get all active exams first (respects security rules)
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      // Filter on client side to avoid complex queries
      final exams = querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .where((exam) =>
              exam.name.toLowerCase().contains(query.toLowerCase()) ||
              exam.examType.toLowerCase().contains(query.toLowerCase()) ||
              (exam.customName?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();

      return exams;
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: User not authorized to search exams');
      }
      throw Exception('Failed to search exams: ${e.message}');
    } catch (e) {
      throw Exception('Failed to search exams: $e');
    }
  }

  /// Stream of active exams (real-time updates with proper error handling)
  static Stream<List<ExamModel>> getActiveExamsStream() {
    if (!isAuthenticated) {
      return Stream.error('User must be authenticated to stream exams');
    }

    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList())
        .handleError((error) {
      if (error is FirebaseException) {
        if (error.code == 'permission-denied') {
          throw Exception('Permission denied: User not authorized to stream exams');
        }
      }
      throw Exception('Failed to stream exams: $error');
    });
  }

  /// Stream of featured exams (real-time updates)
  static Stream<List<ExamModel>> getFeaturedExamsStream({int limit = 5}) {
    if (!isAuthenticated) {
      return Stream.error('User must be authenticated to stream featured exams');
    }

    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      final exams =
          snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList();

      // Return all exams for now (can filter by questions.isNotEmpty if needed)
      return exams;
    }).handleError((error) {
      if (error is FirebaseException) {
        if (error.code == 'permission-denied') {
          throw Exception('Permission denied: User not authorized to stream featured exams');
        }
      }
      throw Exception('Failed to stream featured exams: $error');
    });
  }

  /// Get exam statistics (if user has permission)
  static Future<Map<String, dynamic>> getExamStats() async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to fetch exam stats');
    }

    try {
      // Try to get from exam_stats collection first (cached data)
      final statsDoc = await _firestore.collection('exam_stats').doc('global').get();
      
      if (statsDoc.exists) {
        return statsDoc.data() ?? {};
      }

      // Fallback: Calculate from exams collection (only active exams visible to user)
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      int totalActiveExams = querySnapshot.docs.length;
      int examsWithQuestions = querySnapshot.docs
          .where((doc) {
            final data = doc.data();
            final questions = data['questions'] as List?;
            return questions?.isNotEmpty == true;
          })
          .length;

      return {
        'totalActiveExams': totalActiveExams,
        'examsWithQuestions': examsWithQuestions,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } on FirebaseException catch (e) {
      if (e.code == 'permission-denied') {
        throw Exception('Permission denied: User not authorized to read exam stats');
      }
      throw Exception('Failed to fetch exam stats: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch exam stats: $e');
    }
  }

  /// Check if user has admin permissions (helper method)
  static Future<bool> isUserAdmin() async {
    if (!isAuthenticated) return false;

    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        final role = userData?['role'] as String?;
        return role == 'admin' || role == 'master_admin';
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Validate exam data before submission (client-side validation)
  static bool validateExamData(Map<String, dynamic> examData) {
    final requiredFields = ['name', 'examType', 'numberOfQuestions', 'timeLimit', 'suitableFor', 'isActive'];
    
    // Check required fields
    for (String field in requiredFields) {
      if (!examData.containsKey(field)) {
        return false;
      }
    }

    // Validate data types
    if (examData['name'] is! String ||
        examData['examType'] is! String ||
        examData['numberOfQuestions'] is! int ||
        examData['timeLimit'] is! int ||
        examData['suitableFor'] is! List ||
        examData['isActive'] is! bool) {
      return false;
    }

    // Validate ranges
    if (examData['numberOfQuestions'] <= 0 ||
        examData['timeLimit'] <= 0 ||
        (examData['suitableFor'] as List).isEmpty) {
      return false;
    }

    return true;
  }
}

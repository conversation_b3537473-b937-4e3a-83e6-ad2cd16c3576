import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/exam_model.dart';
import 'auth_debug_service.dart';

class ExamService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'exams';

  /// Debug method to check authentication and permissions
  static Future<void> debugAuthAndPermissions() async {
    await AuthDebugService.debugAuthenticationStatus();
  }

  /// Get all active exams with enhanced error handling
  static Future<List<ExamModel>> getActiveExams() async {
    try {
      print('DEBUG: Attempting to fetch active exams...');

      // Run comprehensive auth debug if user not authenticated
      if (FirebaseAuth.instance.currentUser == null) {
        print('DEBUG: User not authenticated, running auth debug...');
        await debugAuthAndPermissions();
        throw Exception('User not authenticated. Please log in first.');
      }

      print(
          'DEBUG: User authenticated: ${FirebaseAuth.instance.currentUser!.uid}');

      // Try simple query first
      print('DEBUG: Trying simple query without filters...');
      final simpleQuery =
          await _firestore.collection(_collection).limit(1).get();

      print(
          'DEBUG: Simple query successful. Found ${simpleQuery.docs.length} documents');

      // Now try the full query
      print('DEBUG: Trying full query with filters...');
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      print(
          'DEBUG: Full query successful. Found ${querySnapshot.docs.length} active exams');

      return querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .toList();
    } on FirebaseException catch (e) {
      print(
          'DEBUG: FirebaseException - Code: ${e.code}, Message: ${e.message}');

      if (e.code == 'permission-denied') {
        print('DEBUG: Permission denied detected, running full auth debug...');
        await debugAuthAndPermissions();
        throw Exception(
            'Permission denied: Please update your Firebase rules or check authentication.');
      } else if (e.code == 'unavailable') {
        throw Exception(
            'Service unavailable: Please check your internet connection and try again.');
      }
      throw Exception('Firebase error: ${e.message}');
    } catch (e) {
      print('DEBUG: General error: $e');
      throw Exception('Failed to fetch exams: $e');
    }
  }

  /// Get featured exams (exams with questions)
  static Future<List<ExamModel>> getFeaturedExams() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .where((exam) => exam.questions.isNotEmpty)
          .toList();

      return exams;
    } catch (e) {
      throw Exception('Failed to fetch featured exams: $e');
    }
  }

  /// Get exam by ID
  static Future<ExamModel?> getExamById(String examId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(examId).get();

      if (doc.exists) {
        return ExamModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to fetch exam: $e');
    }
  }

  /// Get exams suitable for specific role
  static Future<List<ExamModel>> getExamsForRole(String role) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('suitableFor', arrayContains: role)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .where((exam) => exam.questions.isNotEmpty)
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch exams for role: $e');
    }
  }

  /// Search exams by name or type
  static Future<List<ExamModel>> searchExams(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .where((exam) =>
              exam.name.toLowerCase().contains(query.toLowerCase()) ||
              exam.examType.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return exams;
    } catch (e) {
      throw Exception('Failed to search exams: $e');
    }
  }

  /// Get exam statistics
  static Future<Map<String, dynamic>> getExamStats() async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();

      int totalExams = querySnapshot.docs.length;
      int activeExams = querySnapshot.docs
          .where((doc) => doc.data()['isActive'] == true)
          .length;
      int examsWithQuestions = querySnapshot.docs
          .where((doc) =>
              doc.data()['isActive'] == true &&
              (doc.data()['questions'] as List?)?.isNotEmpty == true)
          .length;

      return {
        'totalExams': totalExams,
        'activeExams': activeExams,
        'examsWithQuestions': examsWithQuestions,
      };
    } catch (e) {
      throw Exception('Failed to fetch exam stats: $e');
    }
  }

  /// Stream of active exams (real-time updates)
  static Stream<List<ExamModel>> getActiveExamsStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList());
  }

  /// Stream of featured exams (real-time updates) - shows recent uploads
  static Stream<List<ExamModel>> getFeaturedExamsStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(5) // Limit to 5 for featured section
        .snapshots()
        .map((snapshot) {
      final exams =
          snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList();

      // Debug: Print featured exams found
      print('DEBUG: Found ${exams.length} featured exams (recent uploads)');
      for (var exam in exams) {
        print(
            '  - ${exam.name}: ${exam.questions.length} questions, created: ${exam.createdAt}');
      }

      // For debugging, return all active exams (not just those with questions)
      // TODO: Change this back to filter only exams with questions in production
      return exams;

      // Original filtering (commented out for debugging):
      // return exams.where((exam) => exam.questions.isNotEmpty).toList();
    });
  }

  /// Stream of recent exams (for quiz list - all active exams)
  static Stream<List<ExamModel>> getRecentExamsStream({int? limit}) {
    var query = _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true);

    if (limit != null) {
      query = query.limit(limit);
    }

    return query.snapshots().map((snapshot) {
      final exams =
          snapshot.docs.map((doc) => ExamModel.fromFirestore(doc)).toList();

      // Debug: Print recent exams found
      print('DEBUG: Found ${exams.length} recent exams for quiz list');

      return exams;
    });
  }
}

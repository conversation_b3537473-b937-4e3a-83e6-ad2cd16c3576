import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/exam_model.dart';

/// Debug service to help identify Firebase permission issues
class DebugExamService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const String _collection = 'exams';

  /// Debug authentication status
  static void debugAuthStatus() {
    final user = _auth.currentUser;
    print('=== DEBUG AUTH STATUS ===');
    print('User authenticated: ${user != null}');
    if (user != null) {
      print('User ID: ${user.uid}');
      print('User email: ${user.email}');
      print('User phone: ${user.phoneNumber}');
      print('Email verified: ${user.emailVerified}');
    }
    print('========================');
  }

  /// Test basic Firestore connection
  static Future<void> testFirestoreConnection() async {
    print('=== TESTING FIRESTORE CONNECTION ===');
    try {
      // Try to read from a simple collection
      final testDoc = await _firestore.collection('test-connection').doc('test').get();
      print('Firestore connection: SUCCESS');
      print('Test document exists: ${testDoc.exists}');
    } catch (e) {
      print('Firestore connection: FAILED');
      print('Error: $e');
    }
    print('===================================');
  }

  /// Test exam collection access with detailed error info
  static Future<List<ExamModel>> debugGetExams() async {
    print('=== DEBUG GET EXAMS ===');
    
    // Check authentication first
    debugAuthStatus();
    
    final user = _auth.currentUser;
    if (user == null) {
      print('ERROR: User not authenticated');
      throw Exception('User must be authenticated');
    }

    try {
      print('Attempting to read from exams collection...');
      
      // Try the simplest possible query first
      final querySnapshot = await _firestore
          .collection(_collection)
          .limit(1)
          .get();

      print('SUCCESS: Basic query worked');
      print('Documents found: ${querySnapshot.docs.length}');

      if (querySnapshot.docs.isNotEmpty) {
        final firstDoc = querySnapshot.docs.first;
        print('First document ID: ${firstDoc.id}');
        print('First document data keys: ${firstDoc.data().keys.toList()}');
      }

      // Now try the full query
      print('Attempting full query with filters...');
      final fullQuery = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      print('SUCCESS: Full query worked');
      print('Active exams found: ${fullQuery.docs.length}');

      final exams = fullQuery.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .toList();

      print('Exams parsed successfully: ${exams.length}');
      for (var exam in exams) {
        print('  - ${exam.name}: ${exam.questions.length} questions');
      }

      return exams;

    } on FirebaseException catch (e) {
      print('FIREBASE ERROR:');
      print('  Code: ${e.code}');
      print('  Message: ${e.message}');
      print('  Plugin: ${e.plugin}');
      
      if (e.code == 'permission-denied') {
        print('PERMISSION DENIED - Checking user document...');
        await _checkUserDocument();
      }
      
      rethrow;
    } catch (e) {
      print('GENERAL ERROR: $e');
      rethrow;
    } finally {
      print('===================');
    }
  }

  /// Check if user document exists and has proper role
  static Future<void> _checkUserDocument() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      print('Checking user document in users collection...');
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      
      if (userDoc.exists) {
        print('User document exists');
        final userData = userDoc.data()!;
        print('User data: $userData');
        print('User role: ${userData['role']}');
      } else {
        print('User document does NOT exist in users collection');
        print('This might be the issue - user needs to be registered first');
      }

      print('Checking admin_users collection...');
      final adminDoc = await _firestore.collection('admin_users').doc(user.uid).get();
      
      if (adminDoc.exists) {
        print('Admin document exists');
        final adminData = adminDoc.data()!;
        print('Admin data: $adminData');
      } else {
        print('Admin document does NOT exist');
      }

    } catch (e) {
      print('Error checking user document: $e');
    }
  }

  /// Test with simplified rules (no filtering)
  static Future<List<ExamModel>> testSimpleRead() async {
    print('=== TESTING SIMPLE READ ===');
    
    try {
      // Most basic read possible
      final snapshot = await _firestore.collection(_collection).get();
      
      print('SUCCESS: Simple read worked');
      print('Total documents: ${snapshot.docs.length}');
      
      final exams = snapshot.docs
          .map((doc) => ExamModel.fromFirestore(doc))
          .toList();
      
      return exams;
      
    } catch (e) {
      print('FAILED: Simple read failed - $e');
      rethrow;
    } finally {
      print('=======================');
    }
  }

  /// Create a test exam to verify write permissions
  static Future<void> testCreateExam() async {
    print('=== TESTING CREATE EXAM ===');
    
    try {
      final testExam = {
        'name': 'Test Exam ${DateTime.now().millisecondsSinceEpoch}',
        'examType': 'Custom',
        'numberOfQuestions': 1,
        'timeLimit': 10,
        'suitableFor': ['Test'],
        'questions': [],
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final docRef = await _firestore.collection(_collection).add(testExam);
      print('SUCCESS: Test exam created with ID: ${docRef.id}');
      
      // Clean up - delete the test exam
      await docRef.delete();
      print('Test exam cleaned up');
      
    } catch (e) {
      print('FAILED: Create exam failed - $e');
    } finally {
      print('========================');
    }
  }

  /// Run all debug tests
  static Future<void> runAllTests() async {
    print('\n🔍 STARTING FIREBASE DEBUG TESTS 🔍\n');
    
    debugAuthStatus();
    await testFirestoreConnection();
    
    try {
      await testSimpleRead();
    } catch (e) {
      print('Simple read test failed, trying debug get exams...');
      try {
        await debugGetExams();
      } catch (e2) {
        print('Debug get exams also failed');
      }
    }
    
    await testCreateExam();
    
    print('\n✅ DEBUG TESTS COMPLETED ✅\n');
  }
}

import 'package:flutter/material.dart';
import '../services/exam_service.dart';
import '../services/comprehensive_debug_service.dart';

class FirebaseDebugWidget extends StatefulWidget {
  const FirebaseDebugWidget({Key? key}) : super(key: key);

  @override
  State<FirebaseDebugWidget> createState() => _FirebaseDebugWidgetState();
}

class _FirebaseDebugWidgetState extends State<FirebaseDebugWidget> {
  bool _isRunning = false;
  String _status = 'Ready to test';
  List<String> _results = [];

  void _addResult(String result) {
    setState(() {
      _results.add(result);
    });
  }

  void _clearResults() {
    setState(() {
      _results.clear();
      _status = 'Ready to test';
    });
  }

  Future<void> _runComprehensiveTest() async {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _status = 'Running comprehensive diagnostics...';
      _results.clear();
    });

    try {
      _addResult('🚨 Starting comprehensive Firebase diagnostics...');
      
      // Run the comprehensive diagnostics
      await ComprehensiveDebugService.runFullDiagnostics();
      _addResult('✅ Diagnostics completed');
      
      // Try to fetch exams
      _addResult('📚 Attempting to fetch exams...');
      final exams = await ExamService.getActiveExams();
      _addResult('✅ SUCCESS: Found ${exams.length} exams');
      
      setState(() {
        _status = 'Test completed successfully!';
      });
      
    } catch (e) {
      _addResult('❌ ERROR: $e');
      setState(() {
        _status = 'Test failed - check console for details';
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _runQuickAuthCheck() async {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _status = 'Checking authentication...';
      _results.clear();
    });

    try {
      await ExamService.debugAuthAndPermissions();
      _addResult('✅ Auth check completed - see console for details');
      
      setState(() {
        _status = 'Auth check completed';
      });
      
    } catch (e) {
      _addResult('❌ Auth check failed: $e');
      setState(() {
        _status = 'Auth check failed';
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testSpecificCollection() async {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _status = 'Testing exams collection access...';
      _results.clear();
    });

    try {
      await ComprehensiveDebugService.testCollectionAccess('exams');
      _addResult('✅ Collection test completed - see console for details');
      
      setState(() {
        _status = 'Collection test completed';
      });
      
    } catch (e) {
      _addResult('❌ Collection test failed: $e');
      setState(() {
        _status = 'Collection test failed';
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _forceRefreshAndRetry() async {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _status = 'Force refreshing and retrying...';
      _results.clear();
    });

    try {
      await ComprehensiveDebugService.forceRefreshAndRetry();
      _addResult('✅ Force refresh completed - see console for details');
      
      setState(() {
        _status = 'Force refresh completed';
      });
      
    } catch (e) {
      _addResult('❌ Force refresh failed: $e');
      setState(() {
        _status = 'Force refresh failed';
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Firebase Debug Tools',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _status,
              style: TextStyle(
                color: _isRunning ? Colors.orange : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Test buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _runComprehensiveTest,
                  icon: const Icon(Icons.bug_report),
                  label: const Text('Full Diagnostics'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _runQuickAuthCheck,
                  icon: const Icon(Icons.person),
                  label: const Text('Auth Check'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testSpecificCollection,
                  icon: const Icon(Icons.storage),
                  label: const Text('Test Collection'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _forceRefreshAndRetry,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Force Refresh'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _clearResults,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Results display
            if (_results.isNotEmpty) ...[
              Text(
                'Results:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: _results.length,
                  itemBuilder: (context, index) {
                    final result = _results[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        result,
                        style: TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                          color: result.startsWith('❌') 
                              ? Colors.red 
                              : result.startsWith('✅') 
                                  ? Colors.green 
                                  : Colors.black,
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Note: Detailed output is in the console/debug logs',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[600],
                ),
              ),
            ],
            
            if (_isRunning)
              const Padding(
                padding: EdgeInsets.only(top: 16),
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

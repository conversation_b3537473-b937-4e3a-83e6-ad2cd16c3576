import 'package:cloud_firestore/cloud_firestore.dart';

class QuestionModel {
  final String id;
  final String question;
  final List<String> options;
  final int correctAnswer;
  final String? explanation;

  const QuestionModel({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    this.explanation,
  });

  factory QuestionModel.fromJson(Map<String, dynamic> json) {
    return QuestionModel(
      id: json['id'] ?? '',
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctAnswer: json['correctAnswer'] ?? 0,
      explanation: json['explanation'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'explanation': explanation,
    };
  }
}

class ExamModel {
  final String id;
  final String name;
  final String examType;
  final String? customName;
  final int numberOfQuestions;
  final int timeLimit;
  final List<String> suitableFor;
  final List<QuestionModel> questions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const ExamModel({
    required this.id,
    required this.name,
    required this.examType,
    this.customName,
    required this.numberOfQuestions,
    required this.timeLimit,
    required this.suitableFor,
    required this.questions,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
  });

  factory ExamModel.fromJson(Map<String, dynamic> json) {
    final questionsData = json['questions'] as List<dynamic>? ?? [];
    final questions = questionsData
        .map((q) => QuestionModel.fromJson(q as Map<String, dynamic>))
        .toList();

    return ExamModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      examType: json['examType'] ?? 'Custom',
      customName: json['customName'],
      numberOfQuestions: json['numberOfQuestions'] ?? 0,
      timeLimit: json['timeLimit'] ?? 30,
      suitableFor: List<String>.from(json['suitableFor'] ?? []),
      questions: questions,
      createdAt:
          DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt:
          DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      isActive: json['isActive'] ?? true,
    );
  }

  factory ExamModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Parse questions
    final questionsData = data['questions'] as List<dynamic>? ?? [];
    final questions = questionsData
        .map((q) => QuestionModel.fromJson(q as Map<String, dynamic>))
        .toList();

    // Parse timestamps
    final createdAt = data['createdAt'] is Timestamp
        ? (data['createdAt'] as Timestamp).toDate()
        : DateTime.now();
    final updatedAt = data['updatedAt'] is Timestamp
        ? (data['updatedAt'] as Timestamp).toDate()
        : DateTime.now();

    return ExamModel(
      id: doc.id,
      name: data['name'] ?? '',
      examType: data['examType'] ?? 'Custom',
      customName: data['customName'],
      numberOfQuestions: data['numberOfQuestions'] ?? 0,
      timeLimit: data['timeLimit'] ?? 30,
      suitableFor: List<String>.from(data['suitableFor'] ?? []),
      questions: questions,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isActive: data['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'examType': examType,
      'customName': customName,
      'numberOfQuestions': numberOfQuestions,
      'timeLimit': timeLimit,
      'suitableFor': suitableFor,
      'questions': questions.map((q) => q.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  /// Convert to map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'examType': examType,
      'customName': customName,
      'numberOfQuestions': numberOfQuestions,
      'timeLimit': timeLimit,
      'suitableFor': suitableFor,
      'questions': questions.map((q) => q.toJson()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
    };
  }
}

// Extension methods for ExamModel
extension ExamModelExtension on ExamModel {
  /// Get display name (custom name if available, otherwise name)
  String get displayName => customName?.isNotEmpty == true ? customName! : name;

  /// Get exam type icon
  String get typeIcon {
    switch (examType) {
      case 'Postal guide':
        return '📮';
      case 'Postal Volumes':
        return '📚';
      default:
        return '📝';
    }
  }

  /// Get difficulty level based on number of questions and time
  String get difficultyLevel {
    final questionsPerMinute = numberOfQuestions / timeLimit;
    if (questionsPerMinute > 1.5) {
      return 'Hard';
    } else if (questionsPerMinute > 1.0) {
      return 'Medium';
    } else {
      return 'Easy';
    }
  }

  /// Get completion percentage (questions added vs target)
  double get completionPercentage {
    if (numberOfQuestions == 0) return 0.0;
    return (questions.length / numberOfQuestions).clamp(0.0, 1.0);
  }

  /// Check if exam is ready (has questions)
  bool get isReady => questions.isNotEmpty;

  /// Get formatted duration
  String get formattedDuration {
    if (timeLimit < 60) {
      return '${timeLimit}m';
    } else {
      final hours = timeLimit ~/ 60;
      final minutes = timeLimit % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}m';
      }
    }
  }

  /// Get suitable roles as formatted string
  String get suitableRolesText => suitableFor.join(', ');

  /// Check if exam is suitable for a specific role
  bool isSuitableFor(String role) => suitableFor.contains(role);

  /// Get exam status
  ExamStatus get status {
    if (!isActive) return ExamStatus.inactive;
    if (questions.isEmpty) return ExamStatus.draft;
    if (questions.length < numberOfQuestions) return ExamStatus.incomplete;
    return ExamStatus.ready;
  }
}

enum ExamStatus {
  draft,
  incomplete,
  ready,
  inactive,
}

extension ExamStatusExtension on ExamStatus {
  String get displayName {
    switch (this) {
      case ExamStatus.draft:
        return 'Draft';
      case ExamStatus.incomplete:
        return 'Incomplete';
      case ExamStatus.ready:
        return 'Ready';
      case ExamStatus.inactive:
        return 'Inactive';
    }
  }

  String get description {
    switch (this) {
      case ExamStatus.draft:
        return 'No questions added yet';
      case ExamStatus.incomplete:
        return 'More questions needed';
      case ExamStatus.ready:
        return 'Ready for students';
      case ExamStatus.inactive:
        return 'Not available to students';
    }
  }
}

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin Users Collection (for web admin panel)
    match /admin_users/{userId} {
      // Allow authenticated users to create their own admin document (for registration)
      allow create: if request.auth != null && request.auth.uid == userId;

      // Allow read/write only if the user is authenticated and accessing their own document
      allow read, update: if request.auth != null && request.auth.uid == userId;

      // Allow super admins and system admins to read/write all admin user documents
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.role in ['super_admin', 'system_admin'];
    }

    // Test Collection (for Firebase connection testing)
    match /test-connection/{docId} {
      // Allow authenticated admin users to read/write test documents
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid));
    }

    // Mobile App Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
      // Allow admin users to read mobile app user data
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Questions collection
    match /questions/{questionId} {
      // Allow all authenticated users to read questions
      allow read: if request.auth != null;

      // Allow mobile app users with admin role to write
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];

      // Allow web admin users to read/write questions
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }

    // Categories collection
    match /categories/{categoryId} {
      // Allow all authenticated users to read categories
      allow read: if request.auth != null;

      // Allow mobile app users with admin role to write
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];

      // Allow web admin users to read/write categories
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Quiz sessions collection
    match /quiz_sessions/{sessionId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Quiz results collection
    match /quiz_results/{resultId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Analytics collection (admin only)
    match /analytics/{analyticsId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Leaderboard collection
    match /leaderboard/{leaderboardId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Daily challenges collection
    match /daily_challenges/{challengeId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // User bookmarks
    match /user_bookmarks/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // App settings (admin only)
    match /app_settings/{settingId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }

    // Exams collection (main collection for quiz data)
    match /exams/{examId} {
      // Allow all authenticated users to read active exams
      allow read: if request.auth != null &&
        resource.data.isActive == true;

      // Allow mobile app users with admin role to create/update exams
      allow create, update: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];

      // Allow web admin users to create/update/delete exams
      allow create, update, delete: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;

      // Allow reading inactive exams for admin users only
      allow read: if request.auth != null &&
        resource.data.isActive == false &&
        (
          (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin']) ||
          (exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true)
        );
    }

    // Exam results/attempts collection
    match /exam_results/{resultId} {
      // Users can read/write their own exam results
      allow read, write: if request.auth != null &&
        resource.data.userId == request.auth.uid;

      // Admin users can read all exam results for analytics
      allow read: if request.auth != null &&
        (
          (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin']) ||
          (exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true)
        );
    }

    // User exam progress tracking
    match /user_progress/{userId} {
      // Users can read/write their own progress
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Admin users can read all user progress for analytics
      allow read: if request.auth != null &&
        (
          (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin']) ||
          (exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true)
        );
    }

    // Exam statistics collection (for caching aggregated data)
    match /exam_stats/{statId} {
      // All authenticated users can read exam statistics
      allow read: if request.auth != null;

      // Only admin users can write/update statistics
      allow write: if request.auth != null &&
        (
          (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin']) ||
          (exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true)
        );
    }
  }
}

# Debug Tools Usage Guide

## 🔧 How to Access Debug Tools

### Method 1: Through Settings (Recommended)
1. Open your Flutter app
2. Navigate to **Settings** tab (bottom navigation)
3. Scroll down to the **About** section
4. Tap on **"Firebase Debug"**
5. This opens the comprehensive debug page

### Method 2: Direct Navigation (For Developers)
```dart
// Add this to any button or method
context.push('/debug');
```

## 🛠️ Available Debug Tools

### 1. 🐛 Full Diagnostics
**What it does:**
- Comprehensive check of Firebase authentication
- Tests Firestore connection and configuration
- Verifies project settings
- Tests different query approaches on exams collection

**When to use:**
- When getting permission denied errors
- First time debugging Firebase issues
- After deploying new rules

**Expected output:**
```
🚨 === COMPREHENSIVE FIREBASE DIAGNOSTICS === 🚨

1️⃣ === FIREBASE AUTH CHECK ===
✅ User authenticated
   - UID: abc123...
   - Token obtained: ✅ (1234 chars)

2️⃣ === FIRESTORE CONNECTION CHECK ===
   - Basic connection: ✅

3️⃣ === PROJECT CONFIGURATION CHECK ===
   - Project ID correct: ✅

4️⃣ === RULES TESTING WITH DIFFERENT APPROACHES ===
   - Exams collection read: ✅ (5 docs)
```

### 2. 👤 Auth Check
**What it does:**
- Quick verification of user authentication status
- Checks user document in Firestore
- Verifies admin permissions

**When to use:**
- Quick auth status check
- Verifying user registration completed properly
- Checking if user document exists in Firestore

### 3. 💾 Test Collection
**What it does:**
- Tests read/write access specifically to exams collection
- Attempts to create a test document
- Verifies collection-level permissions

**When to use:**
- Testing specific collection access
- After updating Firebase rules
- Verifying write permissions

### 4. 🔄 Force Refresh
**What it does:**
- Refreshes Firebase authentication token
- Retries exam collection access
- Clears any cached authentication state

**When to use:**
- When auth token might be expired
- After user has been inactive
- As a last resort for auth issues

## 📊 Understanding Debug Output

### ✅ Success Indicators
- Green checkmarks (✅)
- "SUCCESS" messages
- Specific counts (e.g., "Found 5 exams")

### ❌ Error Indicators
- Red X marks (❌)
- "FAILED" or "ERROR" messages
- Exception details

### ⚠️ Warning Indicators
- Warning symbols (⚠️)
- "WARNING" messages
- Configuration mismatches

## 🔍 Common Issues and Solutions

### Issue 1: User Not Authenticated
**Debug Output:**
```
❌ CRITICAL: No user authenticated!
```

**Solution:**
1. Ensure user has completed login/registration
2. Check if OTP verification was successful
3. Verify authentication flow is working

### Issue 2: Permission Denied
**Debug Output:**
```
❌ Exams collection read: permission-denied
```

**Solutions:**
1. Deploy more permissive Firebase rules
2. Check if user document exists in Firestore
3. Verify user has proper role/permissions

### Issue 3: Project Configuration Mismatch
**Debug Output:**
```
⚠️ WARNING: Project ID mismatch!
Expected: mcq-quiz-system
Actual: some-other-project
```

**Solution:**
1. Check `firebase_options.dart` configuration
2. Verify Firebase project setup
3. Re-run `flutterfire configure` if needed

### Issue 4: Network/Connection Issues
**Debug Output:**
```
❌ Connection failed: unavailable
```

**Solutions:**
1. Check internet connection
2. Verify Firebase project is active
3. Check if Firestore is enabled in Firebase Console

## 📱 Integration in Your App

### Adding Debug Button to Any Screen
```dart
FloatingActionButton(
  onPressed: () => context.push('/debug'),
  child: Icon(Icons.bug_report),
  backgroundColor: Colors.red,
)
```

### Programmatic Debug Check
```dart
// Run diagnostics programmatically
await ExamService.runComprehensiveDiagnostics();

// Quick auth check
await ExamService.debugAuthAndPermissions();

// Test specific collection
await ComprehensiveDebugService.testCollectionAccess('exams');
```

## 🚨 Emergency Debugging Steps

If your app is completely broken:

1. **Run Full Diagnostics**
   - Go to Settings → Firebase Debug → Full Diagnostics
   - Check console output for specific errors

2. **Deploy Open Rules**
   ```bash
   ./deploy-firebase-rules.sh
   # Choose option 4 (Temporary open rules)
   ```

3. **Force Refresh Auth**
   - Use Force Refresh button in debug tools
   - Or sign out and back in

4. **Check Project Configuration**
   - Verify you're connected to correct Firebase project
   - Check `firebase_options.dart` settings

## 📋 Debug Checklist

Before reporting issues, run through this checklist:

- [ ] User is authenticated (Auth Check shows ✅)
- [ ] Firebase connection works (Full Diagnostics shows connection ✅)
- [ ] Project ID is correct (mcq-quiz-system)
- [ ] Firebase rules are deployed and permissive
- [ ] User document exists in Firestore
- [ ] Exams collection has data
- [ ] No network connectivity issues

## 🔧 Advanced Debugging

### Enable Verbose Logging
```dart
// Add to main.dart for more detailed logs
import 'package:firebase_core/firebase_core.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Enable Firebase logging
  await Firebase.initializeApp();
  
  // Your app code...
}
```

### Monitor Firebase Console
1. Go to Firebase Console → Firestore → Usage
2. Check for denied requests
3. Monitor real-time database activity

### Check Network Requests
1. Use Flutter Inspector
2. Monitor network tab for Firebase requests
3. Look for 403 (permission denied) responses

## 📞 Getting Help

If debug tools show persistent issues:

1. **Copy the complete debug output** from console
2. **Note the specific error codes** (permission-denied, unavailable, etc.)
3. **Check Firebase Console** for any project-level issues
4. **Verify your Firebase billing** is active (for production usage)

The debug tools provide comprehensive information to identify and resolve Firebase-related issues quickly.
